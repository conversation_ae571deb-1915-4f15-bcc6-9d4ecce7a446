<?php

namespace Tests\Unit\Controllers\Order;

use App\Http\Controllers\Order\OrderController;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Tests\TestCase;

class OrderControllerElectricityTest extends TestCase
{
    private $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = app(OrderController::class);
    }

    /** @test */
    public function test_check_electricity_order_and_account_returns_success_response()
    {
        $request = Request::create('/', 'POST', ['_user_id' => 12345]);
        
        $response = $this->controller->checkElectricityOrderAndAccount($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('msg', $responseData);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
    }

    /** @test */
    public function test_create_electricity_order_validates_required_fields()
    {
        $request = Request::create('/', 'POST', []);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->createElectricityOrder($request);
    }

    /** @test */
    public function test_create_electricity_order_validates_card_no_format()
    {
        $request = Request::create('/', 'POST', [
            'card_no' => 'invalid',
            'money' => 100.50,
            'connector_id' => 'conn_123',
            'station_id' => 'station_456'
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->createElectricityOrder($request);
    }

    /** @test */
    public function test_create_electricity_order_validates_money_format()
    {
        $request = Request::create('/', 'POST', [
            'card_no' => 123456,
            'money' => 'invalid',
            'connector_id' => 'conn_123',
            'station_id' => 'station_456'
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->createElectricityOrder($request);
    }

    /** @test */
    public function test_create_electricity_order_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'card_no' => 123456,
            'money' => 100.50,
            'connector_id' => 'conn_123',
            'station_id' => 'station_456',
            'password' => 'abc123'
        ]);
        
        $response = $this->controller->createElectricityOrder($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_order_start_electricity_validates_required_fields()
    {
        $request = Request::create('/', 'POST', []);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderStartElectricity($request);
    }

    /** @test */
    public function test_order_start_electricity_validates_start_result_values()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'start_result' => 3,
            'start_time' => '2023-01-01 10:00:00'
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderStartElectricity($request);
    }

    /** @test */
    public function test_order_start_electricity_validates_time_format()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'start_result' => 1,
            'start_time' => 'invalid-time'
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderStartElectricity($request);
    }

    /** @test */
    public function test_order_start_electricity_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'start_result' => 1,
            'start_time' => '2023-01-01 10:00:00',
            'stop_code' => 'stop_123'
        ]);
        
        $response = $this->controller->orderStartElectricity($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /** @test */
    public function test_order_stop_electricity_validates_required_fields()
    {
        $request = Request::create('/', 'POST', []);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderStopElectricity($request);
    }

    /** @test */
    public function test_order_stop_electricity_validates_stop_result_values()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'stop_result' => 5
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderStopElectricity($request);
    }

    /** @test */
    public function test_order_stop_electricity_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'stop_result' => 1
        ]);
        
        $response = $this->controller->orderStopElectricity($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /** @test */
    public function test_order_going_electricity_validates_complex_charge_details()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'connector_id' => 'conn_123',
            'connector_status' => 1,
            'fetch_time' => '2023-01-01 10:00:00',
            'remainder_num' => 50.5,
            'total_money' => 100.0,
            'total_electricity_money' => 80.0,
            'total_service_money' => 20.0,
            'total_num' => 100.0,
            'voltage_a' => 220.0,
            'voltage_b' => 220.0,
            'voltage_c' => 220.0,
            'current_a' => 10.0,
            'current_b' => 10.0,
            'current_c' => 10.0,
            'ChargeDetails' => [
                [
                    'DetailStartTime' => 'invalid-time',
                    'DetailEndTime' => '2023-01-01 11:00:00',
                    'DetailPower' => 50.0
                ]
            ]
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderGoingElectricity($request);
    }

    /** @test */
    public function test_order_going_electricity_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'connector_id' => 'conn_123',
            'connector_status' => 1,
            'fetch_time' => '2023-01-01 10:00:00',
            'remainder_num' => 50.5,
            'total_money' => 100.0,
            'total_electricity_money' => 80.0,
            'total_service_money' => 20.0,
            'total_num' => 100.0,
            'voltage_a' => 220.0,
            'voltage_b' => 220.0,
            'voltage_c' => 220.0,
            'current_a' => 10.0,
            'current_b' => 10.0,
            'current_c' => 10.0
        ]);
        
        $response = $this->controller->orderGoingElectricity($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /** @test */
    public function test_order_finish_electricity_validates_order_detail_structure()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'end_time' => '2023-01-01 12:00:00',
            'stop_reason' => null,
            'num' => 100.0,
            'money' => 100.0,
            'service_money' => 20.0,
            'electricity_money' => 80.0,
            'discounted_money' => 5.0,
            'discounted_service_money' => 2.0,
            'order_detail' => [
                [
                    'start_time' => 'invalid-time',
                    'end_time' => '2023-01-01 11:00:00',
                    'price' => 0.8,
                    'service_price' => 0.2,
                    'total_num' => 50.0,
                    'total_electricity_money' => 40.0,
                    'total_service_money' => 10.0
                ]
            ]
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderFinishElectricity($request);
    }

    /** @test */
    public function test_order_finish_electricity_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            'end_time' => '2023-01-01 12:00:00',
            'stop_reason' => 'User stopped',
            'num' => 100.0,
            'money' => 100.0,
            'service_money' => 20.0,
            'electricity_money' => 80.0,
            'discounted_money' => 5.0,
            'discounted_service_money' => 2.0,
            'order_detail' => []
        ]);
        
        $response = $this->controller->orderFinishElectricity($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /** @test */
    public function test_order_stop_electricity_for_user_validates_user_type()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            '_user_type' => 'invalid_type'
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->orderStopElectricityForUser($request);
    }

    /** @test */
    public function test_order_stop_electricity_for_user_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123,
            '_user_type' => 'driver',
            '_user_id' => 456
        ]);

        $response = $this->controller->orderStopElectricityForUser($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /** @test */
    public function test_get_electricity_order_list_validates_required_fields()
    {
        $request = Request::create('/', 'POST', []);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderList($request);
    }

    /** @test */
    public function test_get_electricity_order_list_validates_user_type()
    {
        $request = Request::create('/', 'POST', [
            '_user_id' => 123,
            '_user_type' => 'invalid_type'
        ]);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderList($request);
    }

    /** @test */
    public function test_get_electricity_order_list_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            '_user_id' => 123,
            '_user_type' => 'driver',
            'page' => 1,
            'limit' => 10
        ]);

        $response = $this->controller->getElectricityOrderList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertIsArray($responseData['data']);
    }

    /** @test */
    public function test_get_electricity_order_list_with_filters_returns_filtered_results()
    {
        $request = Request::create('/', 'POST', [
            '_user_id' => 123,
            '_user_type' => 'admin',
            'page' => 1,
            'limit' => 20,
            'order_status' => [1, 2, 3],
            'start_time' => '2023-01-01 00:00:00',
            'end_time' => '2023-12-31 23:59:59',
            'station_name' => 'Test Station'
        ]);

        $response = $this->controller->getElectricityOrderList($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertIsArray($responseData['data']);
    }

    /** @test */
    public function test_get_electricity_order_progress_validates_order_id()
    {
        $request = Request::create('/', 'POST', []);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderProgress($request);
    }

    /** @test */
    public function test_get_electricity_order_progress_validates_order_id_format()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 'invalid'
        ]);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderProgress($request);
    }

    /** @test */
    public function test_get_electricity_order_progress_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123456789
        ]);

        $response = $this->controller->getElectricityOrderProgress($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_get_electricity_order_info_validates_order_id()
    {
        $request = Request::create('/', 'POST', []);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderInfo($request);
    }

    /** @test */
    public function test_get_electricity_order_info_validates_order_id_format()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 'not_numeric'
        ]);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderInfo($request);
    }

    /** @test */
    public function test_get_electricity_order_info_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123456789
        ]);

        $response = $this->controller->getElectricityOrderInfo($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_get_electricity_order_detail_validates_order_id()
    {
        $request = Request::create('/', 'POST', []);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderDetail($request);
    }

    /** @test */
    public function test_get_electricity_order_detail_validates_order_id_format()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 'invalid_format'
        ]);

        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getElectricityOrderDetail($request);
    }

    /** @test */
    public function test_get_electricity_order_detail_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123456789
        ]);

        $response = $this->controller->getElectricityOrderDetail($request);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
        $this->assertArrayHasKey('data', $responseData);
    }
}
