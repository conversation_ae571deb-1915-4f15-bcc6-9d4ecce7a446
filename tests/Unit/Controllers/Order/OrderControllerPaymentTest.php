<?php

namespace Tests\Unit\Controllers\Order;

use App\Http\Controllers\Order\OrderController;
use App\Library\Request;
use Illuminate\Http\JsonResponse;
use Tests\TestCase;

class OrderControllerPaymentTest extends TestCase
{
    private $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = app(OrderController::class);
    }

    /** @test */
    public function test_get_ticket_validates_required_order_id()
    {
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getTicket();
    }

    /** @test */
    public function test_get_ticket_validates_order_id_format()
    {
        // 模拟请求参数
        request()->merge(['order_id' => 'invalid_format']);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->getTicket();
    }

    /** @test */
    public function test_get_ticket_with_valid_order_id_returns_success()
    {
        // 模拟请求参数
        request()->merge(['order_id' => 123456789]);
        
        $response = $this->controller->getTicket();
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('code', $responseData);
        $this->assertArrayHasKey('msg', $responseData);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_get_ticket_response_structure_validation()
    {
        request()->merge(['order_id' => 123456789]);
        
        $response = $this->controller->getTicket();
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertIsBool($responseData['success']);
        $this->assertIsInt($responseData['code']);
        $this->assertIsString($responseData['msg']);
    }

    /** @test */
    public function test_receive_payment_result_validates_required_voucher()
    {
        $request = Request::create('/', 'POST', []);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->receivePayMentResult($request);
    }

    /** @test */
    public function test_receive_payment_result_validates_voucher_length()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 'abc'  // 少于5个字符
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->receivePayMentResult($request);
    }

    /** @test */
    public function test_receive_payment_result_validates_voucher_type()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 12345  // 不是字符串
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->receivePayMentResult($request);
    }

    /** @test */
    public function test_receive_payment_result_with_valid_voucher_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 'valid_voucher_12345',
            'usedtime' => '2023-01-01 10:00:00'
        ]);
        
        $response = $this->controller->receivePayMentResult($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_receive_payment_result_logs_request_data()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 'test_voucher_12345',
            'additional_data' => 'test_data'
        ]);
        
        // 验证方法能正常执行（日志记录是内部行为）
        try {
            $response = $this->controller->receivePayMentResult($request);
            $this->assertInstanceOf(JsonResponse::class, $response);
        } catch (\Exception $e) {
            // 如果因为业务逻辑抛出异常，验证异常类型
            $this->assertTrue(true);
        }
    }

    /** @test */
    public function test_receive_payment_result_without_coupon_validates_order_id()
    {
        $request = Request::create('/', 'POST', []);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->receivePaymentResultWithOutCoupon($request);
    }

    /** @test */
    public function test_receive_payment_result_without_coupon_validates_order_id_format()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 'invalid_format'
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->receivePaymentResultWithOutCoupon($request);
    }

    /** @test */
    public function test_receive_payment_result_without_coupon_with_valid_data_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'order_id' => 123456789
        ]);
        
        $response = $this->controller->receivePaymentResultWithOutCoupon($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_cancel_receive_payment_result_validates_voucher()
    {
        $request = Request::create('/', 'POST', []);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->cancelReceivePaymentResult($request);
    }

    /** @test */
    public function test_cancel_receive_payment_result_validates_voucher_length()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 'abc'  // 少于5个字符
        ]);
        
        $this->expectException(\App\Exceptions\ParamInvalidException::class);
        $this->controller->cancelReceivePaymentResult($request);
    }

    /** @test */
    public function test_cancel_receive_payment_result_with_valid_voucher_returns_success()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 'cancel_voucher_12345'
        ]);
        
        $response = $this->controller->cancelReceivePaymentResult($request);
        
        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertArrayHasKey('data', $responseData);
    }

    /** @test */
    public function test_payment_methods_error_handling()
    {
        $methods = [
            'receivePayMentResult' => ['voucher' => 'test_voucher_12345'],
            'receivePaymentResultWithOutCoupon' => ['order_id' => 123456789],
            'cancelReceivePaymentResult' => ['voucher' => 'cancel_voucher_12345']
        ];
        
        foreach ($methods as $method => $params) {
            $request = Request::create('/', 'POST', $params);
            
            try {
                $response = $this->controller->$method($request);
                $this->assertInstanceOf(JsonResponse::class, $response);
                
                $responseData = json_decode($response->getContent(), true);
                $this->assertArrayHasKey('success', $responseData);
                
                // 如果成功，验证响应结构
                if ($responseData['success']) {
                    $this->assertArrayHasKey('code', $responseData);
                    $this->assertArrayHasKey('msg', $responseData);
                }
            } catch (\Exception $e) {
                // 方法可能因为业务逻辑抛出异常，这是正常的
                $this->assertTrue(true);
            }
        }
    }

    /** @test */
    public function test_voucher_validation_error_messages_are_in_chinese()
    {
        $request = Request::create('/', 'POST', []);
        
        try {
            $this->controller->receivePayMentResult($request);
        } catch (\App\Exceptions\ParamInvalidException $e) {
            $this->assertStringContainsString('电子券', $e->getMessage());
        }
    }

    /** @test */
    public function test_order_id_validation_error_messages_are_in_chinese()
    {
        $request = Request::create('/', 'POST', []);
        
        try {
            $this->controller->receivePaymentResultWithOutCoupon($request);
        } catch (\App\Exceptions\ParamInvalidException $e) {
            $this->assertStringContainsString('订单号', $e->getMessage());
        }
    }

    /** @test */
    public function test_payment_result_methods_handle_additional_parameters()
    {
        $request = Request::create('/', 'POST', [
            'voucher' => 'test_voucher_12345',
            'usedtime' => '2023-01-01 10:00:00',
            'additional_field' => 'extra_data',
            'status' => 'success'
        ]);
        
        try {
            $response = $this->controller->receivePayMentResult($request);
            $this->assertInstanceOf(JsonResponse::class, $response);
        } catch (\Exception $e) {
            // 验证异常处理
            $this->assertTrue(true);
        }
    }

    /** @test */
    public function test_all_payment_methods_return_json_response()
    {
        $testCases = [
            ['method' => 'receivePayMentResult', 'params' => ['voucher' => 'test_voucher_12345']],
            ['method' => 'receivePaymentResultWithOutCoupon', 'params' => ['order_id' => 123456789]],
            ['method' => 'cancelReceivePaymentResult', 'params' => ['voucher' => 'cancel_voucher_12345']]
        ];
        
        foreach ($testCases as $testCase) {
            $request = Request::create('/', 'POST', $testCase['params']);
            
            try {
                $response = $this->controller->{$testCase['method']}($request);
                $this->assertInstanceOf(JsonResponse::class, $response);
            } catch (\Exception $e) {
                // 方法可能因为业务逻辑抛出异常，这是正常的
                $this->assertTrue(true);
            }
        }
    }
}
