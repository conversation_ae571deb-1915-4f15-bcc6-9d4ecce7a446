<?php


namespace App\Http\Controllers\Order;


use App\Exceptions\ParamInvalidException;
use App\Http\Controllers\Controller;
use App\Http\Defines\CommonError;
use App\Http\Defines\FossRuleDefine;
use App\Library\Helper\Common;
use App\Library\Request;
use App\Models\ElectricityOrderModel;
use App\Models\OrderModel;
use App\Repositories\DictRepository;
use App\Repositories\ElectricityOrderProgressRepository;
use App\Services\CouponService;
use App\Services\ElectricityOrderService;
use App\Services\HistoryService;
use App\Services\NewOrderService;
use App\Services\OrderService;
use App\Services\StationService;
use App\Servitization\FossStation;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use RuntimeException;
use Throwable;

class OrderController extends Controller
{
    protected $newOrderService;
    protected $conponSrv;
    protected $historyService;

    public function __construct
    (
        NewOrderService $newOrderService,
        HistoryService $historyService,
        CouponService $couponSrv
    )
    {
        $this->newOrderService = $newOrderService;
        $this->historyService = $historyService;
        $this->conponSrv = $couponSrv;
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '订单控制器初始化结束',
                'current_time' => (string)microtime(true),
            ]);
        }
    }

    /**
     * 获取商品信息
     *
     * @return JsonResponse
     */
    public function getGoodsLimit()
    {
        $field = [
            'order_token' => '',
            'station_id'  => '',
        ];

        $rule = [
            'order_token' => 'required|numeric',
            'station_id'  => 'required|string',
        ];

        $message = [
            'order_token.required' => '下单token必传',
            'station_id.required'  => '站点ID必传',
            'station_id.string'    => '站点ID格式错误,请核对!'
        ];

        try {
            $params = $this->getParamAll($field);

            $this->validator($params, $rule, $message);

            $result = $this->newOrderService->getGoodsLimit($params);
            return $this->success($result);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }

    }

    /**
     * 校验上笔订单状态
     *
     * @return JsonResponse
     */
    public function checkStationLatestOrder()
    {
        $field = [
            'station_id' => '',
        ];

        $rule = [
            'station_id' => 'required|string'
        ];
        $message = [
            'station_id.required' => '站点ID必传!'
        ];
        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->newOrderService->checkStationLatestOrder($params);
            return $this->success($result);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 下单
     *
     * @return JsonResponse
     */
    public function createOrder()
    {
        $field = [
            'order_token'   => null,
            'order_type'    => null, //订单类型 1司机主动付款 2加油员扫码
            'station_id'    => null, //站点ID
            'oil_type'      => '', //油品类型
            'oil_name'      => '', //油品名称
            'oil_level'     => '', //油品等级
            'oil_unit'      => null, //加油单位 1按元 2按升
            'oil_num'       => null, //加油升数
            'oil_price'     => null, //单价
            'oil_money'     => null, //金额
            'service_price' => 0.00, //服务费单价
            'service_money' => 0.00, //服务费
        ];

        $rule = [
            'order_token'   => 'required|string',
            'order_type'    => 'required|in:1,2',
            'station_id'    => 'required|string',
            'oil_type'      => 'string',
            'oil_name'      => 'string',
            'oil_level'     => 'string',
            'oil_unit'      => 'required|in:1,2',
            'oil_num'       => 'required|money',
            'oil_price'     => 'required|money',
            'oil_money'     => 'required|money',
            'service_price' => 'required|money',
            'service_money' => 'required|money',
        ];

        $message = [
            'order_token.required'   => 'token密钥必传',
            'order_token.string'     => 'token密钥格式错误',
            'order_type.required'    => '订单类型必传',
            'order_type.in'          => '订单类型错误:1司机主动付款,2加油员扫码',
            'oil_type.string'        => '油品类型格式错误',
            'oil_name.string'        => '油品名称格式错误',
            'oil_level.string'       => '油品等级格式错误',
            'oil_unit.required'      => '加油方式必传',
            'oil_unit.in'            => '加油方式错误:1指定金额加油,2指定升数加油',
            'oil_num.required'       => '加油升数必传',
            'oil_num.money'          => '加油升数格式错误',
            'oil_price.required'     => '商品单价必传',
            'oil_price.money'        => '油品单价格式错误,',
            'oil_money.required'     => '商品金额必传',
            'oil_money.money'        => '油品金额格式错误,保留小数点后2位',
            'service_price.required' => '服务费单价必传',
            'service_price.money'    => '服务费单价格式错误,保留小数点后2位',
            'service_money.required' => '服务费金额必传',
            'service_money.money'    => '服务费金额格式错误,保留小数点后2位',
            'operator.required'      => '下单人必传',
            'operator_id.required'   => '下单人ID必传',
        ];

        $params = $this->getParamAll($field);

        try {
            $this->validator($params, $rule, $message);
            $result = $this->newOrderService->createOrder($params);

            return $this->success($result, true, '成功');
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 订单列表
     *
     * @return JsonResponse
     */
    public function getOrderPaginate()
    {
        $field = [
            'station_id'              => '', // 站点ID,逗号分割
            'station_code'            => '', // 站点编码
            'order_id'                => '', // 订单号
            'third_order_id'          => '', // 三方订单号
            'order_status'            => '', // 订单状态,逗号分割
            'driver_name'             => '', // 司机姓名
            'driver_phone'            => '', // 司机手机号
            'truck_no'                => '', // 司机车牌号
            'card_no'                 => '', // 账号 原卡号
            'supplier_code'           => '', // 站点供应商
            'org_code'                => '', // 机构编码
            'need_relevant_org_order' => '', // 是否查看子机构订单 1是 0否
            'oil_name'                => '', // 油品名称
            'oil_type'                => '', // 油品类型
            'oil_level'               => '', // 油品等级
            'province_code'           => '', // 省
            'city_code'               => '', // 市
            'ge_create_time'          => '', // 创建时间开始
            'le_create_time'          => '', // 创建时间截止
            'ge_pay_time'             => '', // 更新时间开始
            'le_pay_time'             => '', // 更新时间截止
            'channel_id'              => '', // 渠道ID
            'history_id'              => '', // 交易流水ID
            'third_coupon_flag'       => '', // 三方券标识
            'g7_coupon_flag'          => '', // G7券标识
            'order_sale_type'         => '', //订单销售类型(10销售订单20预售订单)
            'page'                    => 1,
            'limit'                   => 10
        ];

        try {
            $params = $this->getParamAll($field);

            $header = Request::httpHeaders();

            if (!empty($header['appkey'])) {
                $params['appkey'] = $header['appkey'][0];
            }

            $result = $this->newOrderService->getOrderPaginate($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    public function getMaxDay()
    {
        $maxDay = $this->historyService->getMaxDay();

        return $this->success(['maxDay'=>$maxDay], true);
    }

    /**
     * 订单列表 for pda
     *
     * @return JsonResponse
     */
    public function getOrderPaginateNew()
    {
        $field = [
            'station_id'              => '', // 站点ID,逗号分割
            'station_code'            => '', // 站点编码
            'order_id'                => '', // 订单号
            'third_order_id'          => '', // 三方订单号
            'order_status'            => '', // 订单状态,逗号分割
            'driver_name'             => '', // 司机姓名
            'driver_phone'            => '', // 司机手机号
            'truck_no'                => '', // 司机车牌号
            'card_no'                 => '', // 账号 原卡号
            'supplier_code'           => '', // 站点供应商
            'org_code'                => '', // 机构编码
            'need_relevant_org_order' => '', // 是否查看子机构订单 1是 0否
            'oil_name'                => '', // 油品名称
            'oil_type'                => '', // 油品类型
            'oil_level'               => '', // 油品等级
            'province_code'           => '', // 省
            'city_code'               => '', // 市
            'ge_create_time'          => '', // 创建时间开始
            'le_create_time'          => '', // 创建时间截止
            'ge_pay_time'             => '', // 更新时间开始
            'le_pay_time'             => '', // 更新时间截止
            'channel_id'              => '', // 渠道ID
            'history_id'              => '', // 交易流水ID
            'third_coupon_flag'       => '', // 三方券标识
            'g7_coupon_flag'          => '', // G7券标识
            'order_sale_type'         => '', //订单销售类型(10销售订单20预售订单)
            'page'                    => 1,
            'limit'                   => 10
        ];

        try {
            $params = $this->getParamAll($field);

            $header = Request::httpHeaders();

            if (!empty($header['appkey'])) {
                $params['appkey'] = $header['appkey'][0];
            }
            // 刘鑫力 2025/05/30发邮件 pda订单列表  查询限制：屏蔽掉5月30日之前所有数据
//            站点名称：
//            G42沪蓉--中石化天池服务区左加油站  TDVSKL
//            G42沪蓉--中石化天池服务区右加油站  ML4JUK
            $pass_station_id = ['52d3fc885bf611ecb3f522599c88430d', 'f4b1dec25bf511ecb71b22599c88430d'];
            if(isset($params['station_id']) && in_array($params['station_id'],$pass_station_id)) {
                if($params['ge_create_time'] < '2025-05-31 00:00:00') {
                    $params['ge_create_time'] =  '2025-05-31 00:00:00';
                }
            }

            $maxDay = $this->historyService->getMaxDay();
            if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
                // 不能超过180天
                if(strtotime(date('Y-m-d 00:00:00',time())) - strtotime($params['ge_create_time']) > $maxDay*86400){
                    throw new \Exception('无法查询出该时间段的结果!',40301);
                }
            }

            if (!empty($params['ge_oil_time']) && !empty($params['le_oil_time'])) {
                if (strtotime(date('Y-m-d 00:00:00', time())) - strtotime($params['ge_oil_time']) > $maxDay * 86400) {
                    throw new \Exception('无法查询出该时间段的结果!',40301);
                }
            }

            $result = $this->newOrderService->getOrderPaginate($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 订单列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNewOrderPaginate()
    {
        $field = [
            'station_id' => '', // 站点ID,逗号分割
            'station_code' => '', // 站点编码
            'order_id' => '', // 订单号
            'third_order_id' => '', // 三方订单号
            'order_status' => '', // 订单状态,逗号分割
            'driver_name' => '', // 司机姓名
            'driver_phone' => '', // 司机手机号
            'truck_no' => '', // 司机车牌号
            'card_no' => '', // 账号 原卡号
            'supplier_code' => '', // 站点供应商
            'org_code' => '', // 机构编码
            'need_relevant_org_order' => '', // 是否查看子机构订单 1是 0否
            'oil_name' => '', // 油品名称
            'oil_type' => '', // 油品类型
            'oil_level' => '', // 油品等级
            'province_code' => '', // 省
            'city_code' => '', // 市
            'ge_create_time' => '', // 创建时间开始
            'le_create_time' => '', // 创建时间截止
            'ge_pay_time' => '', // 更新时间开始
            'le_pay_time' => '', // 更新时间截止
            'channel_id' => '', // 渠道ID
            'history_id' => '', // 交易流水ID,
            'order_sale_type' => 'integer', //订单销售类型(10销售订单20预售订单)
            'page' => 1,
            'limit' => 10
        ];

        try {
            $params = $this->getParamAll($field);
            $header = Request::httpHeaders();
            $params['user_id'] = $header['x-g7-api-uid'][0];
            $result = $this->newOrderService->getNewOrderPaginate($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * OA下单
     *
     * @return JsonResponse
     */
    public function createOrderByOA()
    {
        $field = [
            'card_no'        => '',
            'station_id'     => '',
            'oil_type'       => '',
            'oil_name'       => '',
            'oil_level'      => '',
            'oil_unit'       => '',
            'oil_num'        => '',
            'oil_price'      => '',
            'oil_money'      => '',
            'creator'        => '',
            'driver_name'    => '',
            'driver_phone'   => '',
            'truck_no'       => '',
            'third_order_id' => '',
        ];

        $rule = [
            'card_no'        => 'required|string',
            'station_id'     => 'required|string',
            'oil_type'       => 'string',
            'oil_name'       => 'string',
            'oil_level'      => 'string',
            'oil_unit'       => 'required|in:1,2',
            'oil_num'        => 'required|numeric',
            'oil_price'      => 'required|numeric|range_price',
            'oil_money'      => 'required|numeric',
            'creator'        => 'required|string|max:20',
            'driver_name'    => 'string|max:20',
            'driver_phone'   => 'string|max:11',
            'truck_no'       => 'string|max:20',
            'third_order_id' => 'required',
        ];

        $message = [
            'oil_unit.required'       => '加油方式必传:1按金额 2按升',
            'oil_num.required'        => '加油数量必传',
            'oil_price.min'           => '油品单价最低'.FossRuleDefine::MIN_PRICE.'元',
            'oil_price.max'           => '油品单价最高'.FossRuleDefine::MAX_PRICE.'元',
            'creator.required'        => '下单人不能为空',
            'third_order_id.required' => '三方单号不能为空',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $orderId = $this->newOrderService->createOrderByOA($params);
            return $this->success($orderId, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 获取订单详情
     *
     * @return JsonResponse
     */
    public function getOrderItem()
    {
        $field = [
            'order_id' => '',
            'third_order_id' => '',
            'org_code' => '',
        ];

        $rule = [
            'order_id'       => 'required_without:third_order_id',
            'third_order_id' => 'required_without:order_id',
            'org_code'       => 'required_without:order_id|required_with:third_order_id',
        ];

        $message = [
            'order_id.required_without' => '订单号必传',
            #'order_id.integer' => '订单号格式错误!',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->newOrderService->getOrderItem($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 订单关闭
     *
     * @return JsonResponse
     */
    public function orderTimeOut()
    {
        $field = [
            'order_id' => '',
        ];

        $rule = [
            'order_id' => 'required|numeric'
        ];

        $message = [
            'order_id.required' => '订单号必传',
            'order_id.numeric'  => '订单号格式错误!',
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->newOrderService->orderTimeOut($params);
            return $this->success($result);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @return JsonResponse
     */
    public function cancelOrder()
    {
        $field = [
            'order_id'       => '',
            'force'          => 0,
            'pushMsgToDoper' => false,
            'pay_reason'     => '',
        ];
        $rule = [
            'order_id'       => 'required|numeric',
            'force'          => 'in:0,1',
            'pushMsgToDoper' => 'nullable|boolean',
        ];
        $message = [
            'order_id.required'      => '订单号必填',
            'order_id.numeric'       => '订单号格式错误',
            'force.in'               => '确认取消订单,请填1',
            'pushMsgToDoper.boolean' => '是否推送消息给收银员标识错误',
        ];
        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $message);

            $result = $this->newOrderService->cancelOrder($params);
            return $this->success($result);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 批量获取订单详情
     *
     * @return JsonResponse
     */
    public function getBatchOrderItem()
    {
        $field = [
            'order_ids' => []
        ];

        $rule = [
            'order_ids' => 'required|array'
        ];

        try {
            $params = $this->getParamAll($field);
            $this->validator($params, $rule);

            $result = $this->newOrderService->getBatchOrderItem($params);
            return $this->success($result);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 查看小票
     *
     * @return JsonResponse
     */
    public function getTicket()
    {
        $field = [
            'order_id' => ''
        ];
        $rule = [
            'order_id' => 'required|numeric'
        ];
        $message = [
            'order_id.numeric' => '订单号格式错误'
        ];
        $params = $this->getParamAll($field);
        try {
            $this->validator($params, $rule, $message);
            $result = $this->newOrderService->getTicket($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 查询交易流水
     *
     * @return JsonResponse
     */
    public function getHistoryPaginate()
    {
        $rule = [
            'business_log_id'            => 'nullable',              // ID
            'serial_num'                 => 'nullable|alpha_num',         // G7流水ID
            'stream_no'                  => 'nullable',                    // 三方订单号
            'need_relevant_business_log' => 'nullable',   // 查看关联流水 0不需要 1需要[暂时不加]
            'card_no'                    => 'nullable',                      // 账号 原16位卡号
            'ge_create_time'             => 'nullable',               // 流水创建时间(开始)
            'le_create_time'             => 'nullable',               // 流水创建时间(截止)
            'ge_oil_time'                => 'nullable',                  // 订单支付时间(开始)
            'le_oil_time'                => 'nullable',                  // 订单支付时间(截止)
            'station_id'                 => 'nullable',                   // 站点ID,逗号隔开
            'station_code'               => 'nullable',                 // 站点编码
            'log_type'                   => 'nullable',                     // 单据类型
            'driver_name'                => 'nullable',                  // 司机姓名
            'driver_phone'               => 'nullable',                 // 手机号
            'truck_no'                   => 'nullable',                     // 车牌号
            'org_code'                   => 'nullable',                     //司机机构
            'need_relevant_org_order'    => 'nullable|in:0,1', // 查看机构子订单 0否 1是
            'province_code'              => 'nullable',                // 所属省
            'city_code'                  => 'nullable',                    // 所属市
            'data_type'                  => 'nullable',                    // 订单来源
            'pcode'                      => 'nullable',                        // 站点供应商
            'oil_type'                   => 'nullable|alpha_num',           // 油品类型
            'oil_name'                   => 'nullable|alpha_num',           // 油品名称
            'oil_level'                  => 'nullable|alpha_num',          // 油品级别
            'channel_id'                 => 'nullable|integer',           // 采购渠道ID
            'limit'                      => 'nullable|numeric|between:1,100',
            'page'                       => 'nullable|numeric'
        ];

        $params = $this->trimNull(array_keys($rule));

        try {
            $this->validator($params, $rule);
            $stationIdArray = Request::get('station_id_array');
            if (!empty($stationIdArray)) {
                $params['station_id'] = implode(',', $stationIdArray);
            }
            $result = $this->historyService->getHistoryPaginate($params);
            return $this->success($result, true, '成功');
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 统一的下单接口
     *
     */
    public function generateOrder(Request $request)
    {
        $params = $request->all();
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '接参结束',
                'current_time' => (string)microtime(true),
            ]);
        }
        /*//trade_type 1:正常消费,2:客户下单,3:补录
        if( !isset($params['trade_type']) || empty($params['trade_type']) ){
            $params['trade_type'] = 1;
        }*/
        /*//小程序下单，得到操作人
        $header = Request::httpHeaders();
        $user_id = $header['x-g7-api-uid'][0];
        $userList = (new FossUser())->getUserInfo(['uid'=>$user_id ]);
        if( count($userList) > 0 && isset($userList['uid']) && !empty($userList['uid']) ){
            $params['operator_id'] = $user_id;
            $params['operator'] = array_get($userList,"mobile","");
        }*/
        //兼容pda下单
        if( !isset($params['operator_id']) || empty($params['operator_id']) || empty($params['operator']) ) {
            $params['operator_id'] = Request::get('uid');
            $params['operator'] = Request::get('user_name');
        }
        //auto_pay 下单后，是否直接支付，1：支付，2：不支付
        //oil_time:实际加油时间
        $rule = [
            'order_sn'       => 'nullable|string',
            'third_order_id' => 'nullable|string',
            'card_no'        => 'nullable|string',
            'station_id'     => 'required|string',
            'oil_name'       => 'required|string',
            'oil_type'       => 'nullable|string',
            'oil_level'      => 'nullable|string',
            #'trade_type'     => 'required|in:1,2,3',

            'oil_num'       => 'required_if:oil_unit,2|numeric',
            'oil_price'     => 'required|numeric|range_price',
            'oil_money'     => 'required_if:oil_unit,1|numeric',
            'order_channel' => 'required|numeric',
            'oil_unit'      => 'numeric', //定升2/定额1

            'oil_time'        => 'nullable|string|date',
            'amountGun'       => 'numeric',
            'gunNumber'       => 'numeric',
            'priceGun'        => 'numeric',
            'driver_name'     => 'nullable|string|max:20',
            'driver_phone'    => 'nullable|string|max:11',
            'truck_no'        => 'nullable|string|max:20',
            'service_price'   => 'numeric',
            'service_money'   => 'numeric',
            'order_token'     => 'nullable|string',
            'operator'        => 'required',
            'operator_id'     => 'required',
            'third_price'     => 'nullable|numeric',
            'third_pay_money' => 'nullable|numeric',
        ];
        $messages = [
            #'order_sn.required'  => '唯一流水号不能为空',
            #'third_order_id.required'  => '三方单号不能为空',
            'station_id.required'     => '请选择油站',
            'oil_name.required'       => '请选择油品',
            'oil_price.min'           => '油品单价最低'.FossRuleDefine::MIN_PRICE.'元',
            'oil_price.max'           => '油品单价最高'.FossRuleDefine::MAX_PRICE.'元',
            'oil_num.required_if'     => '按升计费时加油数量必传',
            'oil_num.numeric'         => '加油数量不正确',
            'oil_money.required_if'   => '按元计费时加油金额必传',
            'oil_money.numeric'       => '加油金额不正确',
            #'trade_type.required'    => '下单方式不能为空',
            'order_channel.required'  => '交易来源不能为空',
            'operator.required'       => '下单人必传',
            'operator_id.required'    => '下单人ID必传',
            'third_price.numeric'     => '司机实付单价不正确',
            'third_pay_money.numeric' => '司机实付金额不正确',
        ];
        //交易来源 详情见：defines/CardTradeConf.php
        //$params['order_channel'] = array_get($params,'order_channel','202');
        $this->validator($params,$rule,$messages);
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '开始执行下单函数',
                'current_time' => (string)microtime(true),
            ]);
        }
        $result = $this->newOrderService->proxyOrder($params);
        return $this->success($result);
    }

    /**
     * oa下单
     * @param Request $request
     * @return JsonResponse
     */
    // 为了优化订单生成速度，优化历史代码重复读取输入流
    //public function  oaMakeOrder(Request $request)
    public function  oaMakeOrder(\Illuminate\Http\Request $request)
    {
        $params = $request->all();
        //为了兼容框架预处理空字符串为null值
        foreach ($params as $k => &$v) {
            if (in_array($k, [
                'third_order_id',
                'card_no',
                'driver_name',
                'driver_phone',
                'truck_no',
                ]) and is_null($v)) {
                $v = "";
            }
        }
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '接参结束',
                'current_time' => (string)microtime(true),
            ]);
        }
        $rule = [
            'third_order_id' => 'string',
            'card_no'        => 'string',
            'order_no'       => 'nullable|string',
            'station_id'     => 'nullable|string',
            'pcode'          => 'nullable|string',
            'app_station_id' => 'nullable|alpha_dash',
            'client_type'    => 'nullable|string',
            'oil_name'       => 'required|string',
            'oil_type'       => 'nullable|string',
            'oil_level'      => 'nullable|string',
            'oil_unit'       => 'numeric',
            'oil_num'        => 'numeric',
            'oil_price'      => 'required|numeric|range_price',
            'supplier_price' => 'numeric|range_price',
            'oil_money'      => 'numeric',
            'pay_money'      => 'numeric',
            'supplier_money' => 'nullable|numeric',
            'trade_mode'     => 'required|numeric',
            'driver_name'    => 'string|max:20',
            'driver_phone'   => 'string|max:11',
            'driver_source'  => 'required|numeric',
            'truck_no'       => 'string|max:20',
            'service_price'  => 'nullable|numeric',
            'service_money'  => 'nullable|numeric',
            'gunNumber'      => 'nullable|numeric',
            'priceGun'       => 'nullable|numeric',
            'amountGun'      => 'nullable|numeric',
            'ndrc_price'     => 'nullable|numeric|range_price', // 发改委价，仅顺丰必填
        ];
        $messages = [
            'oil_name.required'   => '请选择油品',
            'oil_price.min'       => '油品单价最低'.FossRuleDefine::MIN_PRICE.'元',
            'oil_price.max'       => '油品单价最高'.FossRuleDefine::MAX_PRICE.'元',
            'trade_mode.required' => '交易模式不能为空',
        ];

        $this->validator($params,$rule,$messages);

        // 获取order_channel
        $params['order_channel'] = $this->newOrderService->getOaOrderChannel($params['trade_mode'], $params['client_type'] ?? '');
        if ($rootSpan) {
            $rootSpan->log([
                'title' => '开始执行下单函数',
                'current_time' => (string)microtime(true),
            ]);
        }
        Common::log('info', date('Y-m-d H:i:s').'接收OA下单参数'.var_export($params,true));
        $result = $this->newOrderService->unifiedOrder($params);

        return $this->success($result);
    }

    /**
     * oa支付订单
     * @param Request $request
     * @return JsonResponse
     */
    public function oaPayOrder(Request $request)
    {
        $params = $request->all();

        $rule = [
            'third_order_id' => 'nullable|string',
            'order_id'       => 'required|string',
        ];
        $messages = [
            #'third_order_id.required'  => '唯一流水号不能为空',
            'order_id.required' => '订单ID必传',
        ];

        $this->validator($params, $rule, $messages);
        $result = $this->newOrderService->payOrder($params);
        return $this->success($result);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws RuntimeException
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 5:36 下午
     */
    public function getDriverRefundApplicationRecordList(Request $request): JsonResponse
    {
        $params = $request->all();
        $rules = [
            'page'              => 'required|numeric',
            'limit'             => 'required|numeric',
            'driver_phone'      => 'numeric',
            'order_no'          => 'numeric',
            'platform_order_no' => 'alpha_dash',
        ];
        $messages = [
            'page.required'                => CommonError::PAGE_INVALID,
            'page.numeric'                 => CommonError::PAGE_INVALID,
            'limit.required'               => CommonError::PAGE_LIMIT_INVALID,
            'limit.numeric'                => CommonError::PAGE_LIMIT_INVALID,
            'driver_phone.numeric'         => CommonError::DRIVER_PHONE_INVALID,
            'order_no.numeric'             => CommonError::ORDER_NO_INVALID,
            'platform_order_no.alpha_dash' => CommonError::PLATFORM_ORDER_NO_INVALID,
        ];
        $this->validatorThrowRuntimeException($params, $rules, $messages);
        $result = $this->newOrderService->getDriverRefundApplicationRecordList($params);
        return $this->success($result);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/19 7:15 下午
     */
    public function reviewDriverRefundApplicationRecord(Request $request): JsonResponse
    {
        $params = $request->all();
        $params['user_name'] = Request::get('user_name', '');
        $rules = [
            'id'              => 'required|numeric',
            'approval_status' => 'required|in:1,2',
            'approval_reason' => 'min:0|max:85',
        ];
        $messages = [
            'id.required'              => CommonError::ID_REQUIRED,
            'id.numeric'               => CommonError::ID_INVALID,
            'approval_status.required' => CommonError::APPROVAL_STATUS_REQUIRED,
            'approval_status.in'       => CommonError::APPROVAL_STATUS_INVALID,
            'approval_reason.min'      => CommonError::APPROVAL_REASON_INVALID,
            'approval_reason.max'      => CommonError::APPROVAL_REASON_INVALID,
            'user_name'
        ];
        $this->validatorThrowRuntimeException($params, $rules, $messages);
        $this->newOrderService->reviewDriverRefundApplicationRecord($params);
        return $this->success();
    }

    /**
     * 卡包获取通过枪ID及卡号获取油品单价
     * @param Request $request
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/29 8:14 下午
     */
    public function getOilPriceByGunAndCard(Request $request): JsonResponse
    {
        $params = $request->all();
        $rule = [
            'card_no'  => 'required|string|min:8',
            'oilGunId' => 'required|string|min:8',
        ];
        $messages = [
            'card_no.required'  => "请选择账户",
            'oilGunId.required' => "请选择枪号",
        ];
        $this->validator($params, $rule, $messages);
        return $this->success((new StationService())->getGmsOilPrice($params));
    }

    public function getPayMentQrcode(Request $request):JsonResponse
    {
        $params = $request->all();
        $rule = [
            'pcode'         => 'required',
            'history_id'    => 'required',
        ];
        $messages = [
            'pcode.required'        => "供应商编码不能为空",
            'history_id.required'   => "交易流水ID不能为空",
        ];
        $this->validator($params, $rule, $messages);
        return $this->success($this->newOrderService->getPayMentQrcode($params));
    }

    public function batchGetByOrderId(Request $request):JsonResponse
    {
        $params = $request->all();
        $rule = [
            'order_id'  => 'required',
            'master'    => 'nullable|string'
        ];
        $messages = [
            'order_id.required'  => "请输入G7券标识",
        ];
        $this->validator($params, $rule, $messages);

        $master = empty($params['master']) ? false : true;

        return $this->success(app(OrderService::class)->batchGetByOrderId(explode(',', $params['order_id']), $master));
    }

    /**
     * @param Request $request
     * @throws \App\Exceptions\ParamInvalidException
     * @throws ValidationException
     * 接收电子券核销结果
     */
    public function receivePayMentResult(Request $request)
    {
        $params = $request->all();
        Common::log("info","电子券核销结果",$params);
        $rule = [
            'voucher' => 'required|string|min:5',
        ];
        $messages = [
            'voucher.required' => "电子券标识不能为空",
        ];
        $this->validator($params,$rule,$messages);
        $data = $this->conponSrv->receiveHxResult($params);
        return $this->success($data);
    }

    /**
     * 获取机构对账订单【默认获取昨天全量订单(包括昨天退款订单)，时间跨度最大7天】
     * @param Request $request
     * @return JsonResponse
     */
    public function getOrgBillOrder(Request $request)
    {
        $params = $request->all();
        Common::log("info","获取机构对账订单--start",$params);
        $rule = [
            'org_code'   => 'required|array',
            'date_begin' => 'nullable|required_with:date_end|date_format:"Y-m-d"|after:"2021-06-01"',
            'date_end'   => 'nullable|required_with:date_begin|date_format:"Y-m-d"|after_or_equal:date_begin',
        ];
        $message = [
            'org_code.required' => '机构编码必传',
        ];
        try {
            $this->validator($params, $rule, $message);
            // 因订单未记录退款时间，退款订单暂从foss获取
            $refundOrder = $this->newOrderService->getOrgRefundOrder($params);
            if (!empty($refundOrder)) {
                $params['other_order_ids'] = array_column($refundOrder, 'api_id');
            }

            $retCheckDate = $this->newOrderService->validateQueryOrderBillDate($params);
            $params['ge_create_time'] = $retCheckDate['ge_create_time'];
            $params['le_create_time'] = $retCheckDate['le_create_time'];
            $params['order_status'] = OrderModel::SUCCESS_PAY;

            $result = $this->newOrderService->getBatchOrderItem($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 批量获取订单详情
     *
     * @return JsonResponse
     */
    public function getOrderIdsForJHCX(): JsonResponse
    {
        $field = [
            'supplier_code'  => '',
            'ge_create_time' => '',
            'le_create_time' => '',
        ];
        $rule = [
            'supplier_code'  => 'required|alpha_num',
            'ge_create_time' => 'required|date_format:"Y-m-d H:i:s"',
            'le_create_time' => 'required|date_format:"Y-m-d H:i:s"',
        ];
        $messages = [
            'supplier_code.required'     => '运营商代码不能为空',
            'supplier_code.alpha_num'    => '运营商代码不正确',
            'ge_create_time.required'    => '创建时间起始值不能为空',
            'ge_create_time.date_format' => '创建时间起始值不正确',
            'le_create_time.required'    => '创建时间结束值不能为空',
            'le_create_time.date_format' => '创建时间结束值不正确',
        ];
        try {

            $params = $this->getParamAll($field);
            $this->validator($params, $rule, $messages);
            return $this->success($this->newOrderService->getOrderIdsForJHCX($params));
        } catch (Exception $e) {

            return $this->fail($e->getCode() == 0 ? 500 : $e->getCode(), $e->getMessage());
        }
    }

    public function getOperateOrderReason(): JsonResponse
    {
        try {

            $data = app(DictRepository::class)->getOperateOrderReason();
        } catch (Throwable $throwable) {

            return $this->fail($throwable->getCode() == 0 ? 500 : $throwable->getCode(),
                $throwable->getMessage());
        }
        return $this->success($data);
    }

    public function determineOrderIsDeductionFailed()
    {
        $rule = [
            'order_id' => 'required',
        ];
        try {
            $params = $this->getParamAll($rule);
            $this->validator($params, $rule, [
                'order_id.required' => '订单号必传',
                'order_id.numeric'  => '订单号格式错误!',
            ]);
            return $this->success($this->newOrderService->determineOrderIsDeductionFailed($params['order_id']), true);
        } catch (Exception $e) {
            return $this->fail(empty($e->getCode()) ? 403 : $e->getCode(), $e->getMessage());
        }
    }

    public function getDocumentTypeByHistoryId()
    {
        $rule = [
            'history_id' => 'required|numeric',
        ];
        try {
            $params = $this->getParamAll($rule);
            $this->validator($params, $rule, [
                'history_id.required' => '流水id必传',
                'history_id.numeric'  => '流水id格式错误!',
            ]);
            return $this->success([
                'document_type' => $this->historyService->getDocumentTypeByHistoryId($params['history_id'])
            ], true);
        } catch (Exception $e) {
            return $this->fail(empty($e->getCode()) ? 403 : $e->getCode(), $e->getMessage());
        }
    }

    public function getLimitConfig()
    {
        $this->newOrderService->getLimitStation("200133");
    }

    public function distributeOrderStatus(): JsonResponse
    {
        $rule = [
            'order_id' => 'required|numeric',
        ];
        $params = $this->getParamAll($rule);
        $this->validator($params, $rule, [
            'order_id.required' => '订单ID必传',
            'order_id.numeric'  => '订单ID格式错误!',
        ]);
        try {
            $this->newOrderService->distributeOrderStatus($params['order_id']);
        } catch (Throwable $throwable) {
            return $this->fail($throwable->getCode() == 0 ? 500 : $throwable->getCode(),
                $throwable->getMessage());
        }
        return $this->success([]);
    }

    /**
     * 通过预约加油订单查询关联的销售订单
     * @return JsonResponse
     */
    public function getOrderInfoByReservationOrder()
    {
        $rule = [
            'reservation_order_id'   => 'required|array',
            'reservation_order_id.*' => 'numeric',
        ];
        $message = [
            'reservation_order_id.required'  => '订单号必传',
            'reservation_order_id.array'     => '订单号格式不正确',
            'reservation_order_id.*.numeric' => '订单号格式不正确',
        ];
        try {
            $params = $this->getParamAll([
                'reservation_order_id' => null,
            ]);
            $this->validator($params, $rule, $message);
            $result = $this->newOrderService->getOrderInfoByReservationOrder($params['reservation_order_id']);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 获取订单日维度对账快照数据
     * @return JsonResponse
     */
    public function getDayOrderReconciliationSnapshotData()
    {
        $rule = [
            'org_code'           => 'required|alpha_num',
            'reconciliation_day' => 'required|date_format:"Y-m-d"',
        ];
        $message = [
            'org_code.required'              => '机构代码不能为空',
            'org_code.alpha_num'             => '机构代码格式不正确',
            'reconciliation_day.required'    => '账单日不能为空',
            'reconciliation_day.date_format' => '账单日格式不正确',
        ];
        try {
            $params = $this->getParamAll([
                'org_code'           => null,
                'reconciliation_day' => null,
            ]);
            $this->validator($params, $rule, $message);
            return $this->success(
                $this->newOrderService->getDayOrderReconciliationSnapshotData($params),
                true
            );
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            return $this->fail($code, $e->getMessage());
        }
    }
    /**
     * 处理无优惠券核销结果的接收。
     * @param Request $request 请求对象。
     * @return mixed 成功处理后的结果数据。
     */
    public function receivePaymentResultWithOutCoupon(Request $request)
    {
        $params = $request->all();
        $rule = [
            'order_id' => 'required|numeric',
        ];
        $messages = [
            'order_id.required' => "订单号不能为空",
            'order_id.required' => "订单号格式不正确",
        ];
        $this->validator($params, $rule, $messages);
        $data = $this->newOrderService->receivePaymentResultWithOutCoupon($params['order_id']);
        return $this->success($data);
    }

    public function cancelReceivePaymentResult(Request $request)
    {
        $params = $request->all();
        $this->validator($params,[
            'voucher' => 'required|string|min:5',
        ],[
            'voucher.required' => "电子券标识不能为空",
        ]);
        return $this->success($this->conponSrv->cancelReceivePaymentResult($params));
    }

    public function getAssignListByOrderId(Request $request)
    {
        $params = $request->all();
        $rule = [
            'order_id' => 'required|numeric',
        ];
        $messages = [
            'order_id.required' => "订单号不能为空",
            'order_id.required' => "订单号格式不正确",
        ];
        $this->validator($params, $rule, $messages);
        return $this->success($this->newOrderService->getAssignList($params) ?? [], true);
    }

    /**
     * 订单列表 for pda合作加油
     *
     * @return JsonResponse
     */
    public function getCooperateOrderPaginate()
    {
        $field = [
            'station_id'              => '', // 站点ID,逗号分割
            'station_code'            => '', // 站点编码
            'order_id'                => '', // 订单号
            'third_order_id'          => '', // 三方订单号
            'order_status'            => '', // 订单状态,逗号分割
            'driver_name'             => '', // 司机姓名
            'driver_phone'            => '', // 司机手机号
            'truck_no'                => '', // 司机车牌号
            'card_no'                 => '', // 账号 原卡号
            'supplier_code'           => '', // 站点供应商
            'orgcode'                => '', // 机构编码
            'need_relevant_org_order' => '', // 是否查看子机构订单 1是 0否
            'oil_name'                => '', // 油品名称
            'oil_type'                => '', // 油品类型
            'oil_level'               => '', // 油品等级
            'province_code'           => '', // 省
            'city_code'               => '', // 市
            'ge_create_time'          => '', // 创建时间开始
            'le_create_time'          => '', // 创建时间截止
            'ge_pay_time'             => '', // 更新时间开始
            'le_pay_time'             => '', // 更新时间截止
            'channel_id'              => '', // 渠道ID
            'history_id'              => '', // 交易流水ID
            'third_coupon_flag'       => '', // 三方券标识
            'g7_coupon_flag'          => '', // G7券标识
            'order_sale_type'         => '', //订单销售类型(10销售订单20预售订单)
            'page'                    => 1,
            'limit'                   => 10
        ];

        try {
            $params = $this->getParamAll($field);

            $header = Request::httpHeaders();

            if (!empty($header['appkey'])) {
                $params['appkey'] = $header['appkey'][0];
            }

            $maxDay = $this->historyService->getMaxDay();
            if (!empty($params['ge_create_time']) && !empty($params['le_create_time'])) {
                // 不能超过180天
                if(strtotime(date('Y-m-d 00:00:00',time())) - strtotime($params['ge_create_time']) > $maxDay*86400){
                    throw new \Exception('无法查询出该时间段的结果!',40301);
                }
            }

            if (!empty($params['ge_oil_time']) && !empty($params['le_oil_time'])) {
                if (strtotime(date('Y-m-d 00:00:00', time())) - strtotime($params['ge_oil_time']) > $maxDay * 86400) {
                    throw new \Exception('无法查询出该时间段的结果!',40301);
                }
            }

            $uid = $header['x-g7-api-uid'] ?? '';
            $xtoken = $header['x-g7-api-token'] ?? '';
            // 只允许查询当前列表的加油员名下的站点关联的orgcode
            $cooperateList = (new FossStation())->getCooperateList([], $uid, $xtoken);
            $orgcodes = [];
            foreach ($cooperateList['data'] as $item) {
                $orgcodes[] = $item['orgcode'];
            }
            if (empty($params['orgcode'])) {
                $params['org_code'] = $orgcodes;
            } elseif (!in_array($params['orgcode'], $orgcodes)) {
                throw new \Exception('无当前车队查询权限!',40301);
            } else {
                $params['org_code'] = $params['orgcode'];
            }
            // 只查合作加油的订单  order_channel = CASHIER_ENTER  901
            $params['order_channel_cooperate'] = 901;


            $result = $this->newOrderService->getOrderPaginate($params);
            return $this->success($result, true);
        } catch (Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }


    public function cashierEnterOrder(\Illuminate\Http\Request $request)
    {
        $params = $request->all();
        $rule = [
            'station_id'   => 'required|string',
            'oil_name'     => 'required|string',
            'oil_type'     => 'nullable|string',
            'oil_level'    => 'nullable|string',
            'oil_num'      => 'required_if:oil_unit,2|numeric',
            'oil_unit'     => 'required|in:1,2',
            'mac_money'    => 'required_if:oil_unit,1|numeric',
            'driver_name'  => 'required|string|max:20',
            'driver_phone' => 'required|string|phone_no',
            'truck_no'     => 'required|string|plate_number',
            'org_code'     => 'required|alpha_num',
            'ocr_url'      => 'required|string',
        ];
        $messages = [
            'oil_name.required'     => '请选择油品',
            'oil_num.required_if'   => '按升计费时加油数量必传',
            'oil_num.numeric'       => '加油数量不正确',
            'mac_money.required_if' => '按元计费时加油金额必传',
            'mac_money.numeric'     => '加油金额不正确',
            'driver_name.required'  => '司机姓名必传',
            'driver_phone.required' => '司机手机号必传',
            'driver_phone.phone_no' => '手机号格式错误,请核对',
            'truck_no.required'     => '车牌号必传',
            'truck_no.plate_number' => '车牌号格式错误,请核对',
            'org_code.required'     => '机构代码必传',
            'org_code.alpha_num'    => '机构代码格式不正确',
            'oil_unit.required'     => '加油方式必传 1按金额 2按升',
            'oil_unit.in'           => '加油方式错误 1按金额 2按升',
            'station_id.required'   => '站点ID必传',
            'station_id.string'     => '站点ID格式不正确',
            'ocr_url.required'      => 'ocr图片必传',
            'ocr_url.string'        => 'ocr图片格式不正确',
        ];
        $this->validator($params, $rule, $messages);
        $params['operator_id'] = Request::get('uid');
        $params['operator'] = Request::get('user_name');
        $result = $this->newOrderService->cashierEnterOrder($params);
        return $this->success($result);
    }

    public function checkElectricityOrderAndAccount(\Illuminate\Http\Request $request): JsonResponse
    {
        return $this->success(
            app(ElectricityOrderService::class)->checkElectricityOrderAndAccount(
                $request->post('_user_id')
            )
        );
    }

    /**
     * @throws ValidationException
     * @throws Exception
     * @throws Throwable
     */
    public function createElectricityOrder(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'card_no'      => 'required|integer',
            'money'        => 'required|numeric',
            'connector_id' => 'required|alpha_dash',
            'station_id'   => 'required|alpha_dash',
            'password'     => 'nullable|alpha_num',
        ], [
            'card_no.required'        => '卡号不能为空',
            'card_no.integer'         => '卡号格式不正确',
            'money.required'          => '金额不能为空',
            'money.numeric'           => '金额格式不正确',
            'connector_id.required'   => '充电桩ID不能为空',
            'connector_id.alpha_dash' => '充电桩ID格式不正确',
            'station_id.required'     => '站点ID不能为空',
            'station_id.alpha_dash'   => '站点ID格式不正确',
            'password.alpha_num'      => '密码不正确',
        ]);
        return $this->success(app(ElectricityOrderService::class)->createElectricityOrder($params), true);
    }

    /**
     * @throws ValidationException
     * @throws Throwable
     */
    public function orderStartElectricity(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id'     => 'required|integer',
            'stop_code'    => 'nullable|alpha_dash',
            'start_result' => 'required|in:1,2',
            'start_time'   => 'required|date_format:Y-m-d H:i:s',
        ], [
            'order_id.required'      => '订单ID不能为空',
            'order_id.integer'       => '订单ID格式不正确',
            'stop_code.alpha_dash'   => '停止码格式不正确',
            'start_time.date_format' => '开始时间格式不正确',
            'start_result.required'  => '开始结果不能为空',
            'start_result.boolean'   => '开始结果格式不正确',
        ]);
        app(ElectricityOrderService::class)->orderStartElectricity($params);
        return $this->success([], true);
    }

    /**
     * @throws Throwable
     * @throws ValidationException
     */
    public function orderStopElectricity(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id'    => 'required|integer',
            'stop_result' => 'required|in:1,2',
        ], [
            'order_id.required'    => '订单ID不能为空',
            'order_id.integer'     => '订单ID格式不正确',
            'stop_result.required' => '停止结果不能为空',
            'stop_result.in'       => '停止结果格式不正确',
        ]);
        app(ElectricityOrderService::class)->orderStopElectricity($params);
        return $this->success([], true);
    }

    /**
     * @throws Throwable
     * @throws ValidationException
     */
    public function orderGoingElectricity(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id'                          => 'required|integer',
            'connector_id'                      => 'required|alpha_dash',
            'connector_status'                  => 'required|in:' . implode(
                    ',',
                    array_keys(ELectricityOrderProgressRepository::$statusMap)
                ),
            'current_a'                         => 'numeric',
            'current_b'                         => 'numeric',
            'current_c'                         => 'numeric',
            'fetch_time'                        => 'required|date_format:Y-m-d H:i:s',
            'remainder_num'                     => 'required|numeric',
            'total_money'                       => 'required|numeric',
            'total_electricity_money'           => 'required|numeric',
            'total_service_money'               => 'required|numeric',
            'total_num'                         => 'required|numeric',
            'voltage_a'                         => 'required|numeric',
            'voltage_b'                         => 'required|numeric',
            'voltage_c'                         => 'required|numeric',
            'ChargeDetails'                     => 'array|nullable',
            'ChargeDetails.*.DetailStartTime'   => 'required|date_format:"Y-m-d H:i:s"',
            'ChargeDetails.*.DetailEndTime'     => 'required|date_format:"Y-m-d H:i:s"',
            'ChargeDetails.*.ElecPrice'         => 'nullable|numeric',
            'ChargeDetails.*.SevicePrice'       => 'nullable|numeric',
            'ChargeDetails.*.DetailPower'       => 'required|numeric',
            'ChargeDetails.*.DetailElecMoney'   => 'nullable|numeric',
            'ChargeDetails.*.DetailSeviceMoney' => 'nullable|numeric',
        ], [
            'order_id.required'                           => '订单ID不能为空',
            'order_id.integer'                            => '订单ID格式不正确',
            'connector_id.required'                       => '充电桩接口ID不能为空',
            'connector_id.alpha_dash'                     => '充电桩接口ID格式不正确',
            'connector_status.required'                   => '充电桩接口状态不能为空',
            'fetch_time.required'                         => '取电时间不能为空',
            'fetch_time.date_format'                      => '取电时间格式不正确',
            'remainder_num.required'                      => '剩余电量不能为空',
            'remainder_num.numeric'                       => '剩余电量格式不正确',
            'total_money.required'                        => '总金额不能为空',
            'total_money.numeric'                         => '总金额格式不正确',
            'total_electricity_money.required'            => '总电费不能为空',
            'total_electricity_money.numeric'             => '总电费格式不正确',
            'total_service_money.required'                => '总服务费不能为空',
            'total_service_money.numeric'                 => '总服务费格式不正确',
            'total_num.required'                          => '总电量不能为空',
            'total_num.numeric'                           => '总电量格式不正确',
            'voltage_a.required'                          => '电压A不能为空',
            'voltage_a.numeric'                           => '电压A格式不正确',
            'voltage_b.required'                          => '电压B不能为空',
            'voltage_b.numeric'                           => '电压B格式不正确',
            'voltage_c.required'                          => '电压C不能为空',
            'voltage_c.numeric'                           => '电压C格式不正确',
            'current_a.required'                          => '电流A不能为空',
            'current_a.numeric'                           => '电流A格式不正确',
            'current_b.required'                          => '电流B不能为空',
            'current_b.numeric'                           => '电流B格式不正确',
            'current_c.required'                          => '电流C不能为空',
            'current_c.numeric'                           => '电流C格式不正确',
            'ChargeDetails.array'                         => '充电详情格式不正确',
            'ChargeDetails.*.DetailStartTime.required'    => '充电详情开始时间不能为空',
            'ChargeDetails.*.DetailStartTime.date_format' => '充电详情开始时间格式不正确',
            'ChargeDetails.*.DetailEndTime.required'      => '充电详情结束时间不能为空',
            'ChargeDetails.*.DetailEndTime.date_format'   => '充电详情结束时间格式不正确',
            'ChargeDetails.*.ElecPrice.numeric'           => '充电详情电价格式不正确',
            'ChargeDetails.*.SevicePrice.numeric'         => '充电详情服务费格式不正确',
            'ChargeDetails.*.DetailPower.required'        => '充电详情电量不能为空',
            'ChargeDetails.*.DetailPower.numeric'         => '充电详情电量格式不正确',
            'ChargeDetails.*.DetailElecMoney.numeric'     => '充电详情电费格式不正确',
            'ChargeDetails.*.DetailSeviceMoney.numeric'   => '充电详情服务费格式不正确',
        ]);
        app(ElectricityOrderService::class)->orderGoingElectricity($params);
        return $this->success([], true);
    }

    /**
     * @throws Throwable
     * @throws ValidationException
     */
    public function orderFinishElectricity(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id'                               => 'required|integer',
            'end_time'                               => 'required|date_format:Y-m-d H:i:s',
            'stop_reason'                            => 'present|nullable',
            'num'                                    => 'required|numeric',
            'money'                                  => 'required|numeric',
            'service_money'                          => 'required|numeric',
            'electricity_money'                      => 'required|numeric',
            'discounted_money'                       => 'required|numeric',
            'discounted_service_money'               => 'required|numeric',
            'discounted_electricity_money'           => 'required|numeric',
            'order_detail'                           => 'present|array|nullable',
            'order_detail.*.start_time'              => 'required|date_format:"Y-m-d H:i:s"',
            'order_detail.*.end_time'                => 'required|date_format:"Y-m-d H:i:s"',
            'order_detail.*.price'                   => 'required|numeric',
            'order_detail.*.service_price'           => 'required|nullable|numeric',
            'order_detail.*.total_num'               => 'required|numeric',
            'order_detail.*.total_electricity_money' => 'required|numeric',
            'order_detail.*.total_service_money'     => 'required|numeric',
        ], [
            'order_id.required'                               => '订单ID不能为空',
            'order_id.integer'                                => '订单ID格式不正确',
            'end_time.required'                               => '结束时间不能为空',
            'end_time.date_format'                            => '结束时间格式不正确',
            'stop_reason.present'                             => '结束原因不能为空',
            'num.required'                                    => '电量不能为空',
            'num.numeric'                                     => '电量格式不正确',
            'money.required'                                  => '金额不能为空',
            'money.numeric'                                   => '金额格式不正确',
            'service_money.required'                          => '服务费不能为空',
            'service_money.numeric'                           => '服务费格式不正确',
            'electricity_money.required'                      => '电费不能为空',
            'electricity_money.numeric'                       => '电费格式不正确',
            'discounted_money.required'                       => '优惠金额不能为空',
            'discounted_money.numeric'                        => '优惠金额格式不正确',
            'discounted_service_money.required'               => '优惠服务费不能为空',
            'discounted_service_money.numeric'                => '优惠服务费格式不正确',
            'discounted_electricity_money.required'           => '优惠电费不能为空',
            'discounted_electricity_money.numeric'            => '优惠电费格式不正确',
            'order_detail.present'                            => '充电详情不能为空',
            'order_detail.array'                              => '充电详情格式不正确',
            'order_detail.*.start_time.required'              => '充电详情开始时间不能为空',
            'order_detail.*.start_time.date_format'           => '充电详情开始时间格式不正确',
            'order_detail.*.end_time.required'                => '充电详情结束时间不能为空',
            'order_detail.*.end_time.date_format'             => '充电详情结束时间格式不正确',
            'order_detail.*.price.required'                   => '充电详情电单价格不能为空',
            'order_detail.*.price.numeric'                    => '充电详情电价格式不正确',
            'order_detail.*.service_price.required'           => '充电详情服务费不能为空',
            'order_detail.*.service_price.numeric'            => '充电详情服务费格式不正确',
            'order_detail.*.total_num.required'               => '充电详情电量不能为空',
            'order_detail.*.total_num.numeric'                => '充电详情电量格式不正确',
            'order_detail.*.total_electricity_money.required' => '充电详情电费不能为空',
            'order_detail.*.total_electricity_money.numeric'  => '充电详情电费格式不正确',
            'order_detail.*.total_service_money.required'     => '充电详情服务费不能为空',
            'order_detail.*.total_service_money.numeric'      => '充电详情服务费格式不正确',
        ]);
        app(ElectricityOrderService::class)->orderFinishElectricity($params);
        return $this->success([], true);
    }

    /**
     * @throws ValidationException
     * @throws \Throwable
     */
    public function orderStopElectricityForUser(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id'   => 'required|numeric',
            '_user_type' => 'required|in:driver,employee',
        ], [
            'order_id.required'   => "订单号不能为空",
            'order_id.numeric'    => "订单号格式不正确",
            '_user_type.required' => "用户类型不能为空",
            '_user_type.in'       => "用户类型格式不正确",
        ]);
        app(ElectricityOrderService::class)->orderStopElectricityForUser(
            (int)$params['order_id'],
            $params['_user_id'],
            $params['_user_type'] ?? ''
        );
        return $this->success([], true);
    }

    /**
     * @throws Throwable
     * @throws ValidationException
     */
    public function getElectricityOrderList(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id'           => 'numeric',
            'station_id'         => 'nullable|alpha_num',
            'third_connector_id' => 'nullable|alpha_dash',
            'supplier_order_id'  => 'nullable|alpha_dash',
            'order_status'       => 'nullable|array',
            'order_status.*'     => 'in:' . implode(
                    ',',
                    array_keys(ElectricityOrderModel::$orderStatus)
                ),
            'order_status_group' => 'nullable|in:1,2,3',
            'supplier_code'      => 'nullable|alpha_num',
            'org_code'           => 'nullable|alpha_num',
            'create_time_start'  => 'nullable|date_format:"Y-m-d H:i:s"',
            'create_time_end'    => 'nullable|date_format:"Y-m-d H:i:s"',
            'pay_time_start'     => 'nullable|date_format:"Y-m-d H:i:s"',
            'pay_time_end'       => 'nullable|date_format:"Y-m-d H:i:s"',
            'driver_name'        => 'nullable|string',
            'driver_phone'       => 'nullable|numeric',
            'truck_no'           => 'nullable|string',
            'card_no'            => 'nullable|numeric',
            '_user_type'         => 'required|in:driver,employee',
        ], [
            'order_id.numeric'              => '订单号格式不正确',
            'station_id.alpha_num'          => '站点ID格式不正确',
            'third_connector_id.alpha_dash' => '充电桩ID格式不正确',
            'supplier_order_id.alpha_dash'  => '供应商订单号格式不正确',
            'order_status.in'               => '订单状态格式不正确',
            'order_status.array'            => '订单状态格式不正确',
            'order_status.*.in'             => '订单状态格式不正确',
            'order_status_group.in'         => '订单状态分组格式不正确',
            'supplier_code.alpha_num'       => '供应商代码格式不正确',
            'org_code.alpha_num'            => '机构代码格式不正确',
            'create_time_start.date_format' => '创建时间开始格式不正确',
            'create_time_end.date_format'   => '创建时间结束格式不正确',
            'pay_time_start.date_format'    => '支付时间开始格式不正确',
            'pay_time_end.date_format'      => '支付时间结束格式不正确',
            'driver_name.string'            => '司机姓名格式不正确',
            'driver_phone.numeric'          => '司机手机号格式不正确',
            'truck_no.string'               => '车牌号格式不正确',
            'card_no.numeric'               => '卡号格式不正确',
            '_user_type.required'           => "用户类型不能为空",
            '_user_type.in'                 => "用户类型格式不正确",
        ]);
        return $this->success(
            app(ElectricityOrderService::class)->getElectricityOrderList($params),
            true
        );
    }

    /**
     * @throws ValidationException
     */
    public function getElectricityOrderProgress(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id' => 'required|numeric',
        ], [
            'order_id.required' => "订单号不能为空",
            'order_id.numeric'  => "订单号格式不正确",
        ]);
        return $this->success(
            app(ElectricityOrderService::class)->getElectricityOrderProgress($params),
            true
        );
    }

    /**
     * @throws ValidationException
     */
    public function getElectricityOrderInfo(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id' => 'required|numeric',
        ], [
            'order_id.required' => "订单号不能为空",
            'order_id.numeric'  => "订单号格式不正确",
        ]);
        return $this->success(
            app(ElectricityOrderService::class)->getElectricityOrderInfo($params),
            true
        );
    }

    /**
     * @throws ValidationException
     */
    public function getElectricityOrderDetail(\Illuminate\Http\Request $request): JsonResponse
    {
        $params = $request->all();
        $this->validator($params, [
            'order_id' => 'required|numeric',
        ], [
            'order_id.required' => "订单号不能为空",
            'order_id.numeric'  => "订单号格式不正确",
        ]);
        return $this->success(
            app(ElectricityOrderService::class)->getElectricityOrderDetail($params),
            true
        );
    }
}